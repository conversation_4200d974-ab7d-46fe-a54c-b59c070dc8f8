import 'package:flutter/material.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart';
import '../widgets/progress_indicator.dart';
import '../theme/app_theme.dart';
import 'prediction_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: CustomScrollView(
              slivers: [
                _buildAppBar(context),
                SliverPadding(
                  padding: const EdgeInsets.all(16),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildWelcomeSection(context),
                      const SizedBox(height: 24),
                      _buildStatsSection(context),
                      const SizedBox(height: 24),
                      _buildFeaturesSection(context),
                      const SizedBox(height: 24),
                      _buildQuickActionsSection(context),
                      const SizedBox(height: 24),
                      _buildProgressSection(context),
                      const SizedBox(height: 32),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingButton(
        icon: Icons.add,
        onPressed: () => _showNewPredictionDialog(context),
        tooltip: 'Nouvelle prédiction',
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);

    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Predict AV',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.primaryColor,
          ),
        ),
        centerTitle: true,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () {},
        ),
        IconButton(icon: const Icon(Icons.settings_outlined), onPressed: () {}),
      ],
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    final theme = Theme.of(context);

    return GradientCard(
      colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bienvenue dans Predict AV',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Votre assistant intelligent pour les prédictions et analyses avancées',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 16),
          ModernButton(
            text: 'Commencer',
            onPressed: () => _navigateToNewPrediction(context),
            backgroundColor: Colors.white,
            textColor: AppTheme.primaryColor,
            icon: Icons.arrow_forward,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statistiques',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'Prédictions',
                value: '24',
                icon: Icons.analytics,
                color: AppTheme.primaryColor,
                subtitle: '+12% ce mois',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'Précision',
                value: '94%',
                icon: Icons.track_changes,
                color: AppTheme.successColor,
                subtitle: 'Moyenne',
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'Modèles',
                value: '8',
                icon: Icons.model_training,
                color: AppTheme.accentColor,
                subtitle: 'Actifs',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatCard(
                title: 'Temps',
                value: '2.3s',
                icon: Icons.speed,
                color: AppTheme.warningColor,
                subtitle: 'Moyen',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeaturesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Fonctionnalités',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.1,
          children: [
            FeatureCard(
              icon: Icons.trending_up,
              title: 'Analyse Tendances',
              description: 'Analysez les tendances et patterns',
              onTap: () => _navigateToAnalysis(context),
              iconColor: AppTheme.primaryColor,
            ),
            FeatureCard(
              icon: Icons.psychology,
              title: 'IA Prédictive',
              description: 'Intelligence artificielle avancée',
              onTap: () => _navigateToAI(context),
              iconColor: AppTheme.secondaryColor,
            ),
            FeatureCard(
              icon: Icons.bar_chart,
              title: 'Rapports',
              description: 'Générez des rapports détaillés',
              onTap: () => _navigateToReports(context),
              iconColor: AppTheme.accentColor,
            ),
            FeatureCard(
              icon: Icons.settings_suggest,
              title: 'Optimisation',
              description: 'Optimisez vos modèles',
              onTap: () => _navigateToOptimization(context),
              iconColor: AppTheme.successColor,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Actions Rapides',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ModernCard(
          child: Column(
            children: [
              IconTextButton(
                icon: Icons.add_chart,
                text: 'Nouvelle Prédiction',
                onPressed: () => _navigateToNewPrediction(context),
                alignment: MainAxisAlignment.start,
              ),
              const Divider(),
              IconTextButton(
                icon: Icons.upload_file,
                text: 'Importer Données',
                onPressed: () => _importData(context),
                alignment: MainAxisAlignment.start,
              ),
              const Divider(),
              IconTextButton(
                icon: Icons.history,
                text: 'Historique',
                onPressed: () => _navigateToHistory(context),
                alignment: MainAxisAlignment.start,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Progression',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ModernCard(
          child: Column(
            children: [
              ModernProgressIndicator(
                value: 0.75,
                label: 'Formation du modèle',
                showPercentage: true,
                valueColor: AppTheme.primaryColor,
              ),
              const SizedBox(height: 16),
              ModernProgressIndicator(
                value: 0.60,
                label: 'Analyse des données',
                showPercentage: true,
                valueColor: AppTheme.accentColor,
              ),
              const SizedBox(height: 16),
              ModernProgressIndicator(
                value: 0.90,
                label: 'Validation',
                showPercentage: true,
                valueColor: AppTheme.successColor,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showNewPredictionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nouvelle Prédiction'),
        content: const Text('Voulez-vous créer une nouvelle prédiction ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ModernButton(
            text: 'Créer',
            onPressed: () {
              Navigator.pop(context);
              _navigateToNewPrediction(context);
            },
          ),
        ],
      ),
    );
  }

  void _navigateToNewPrediction(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PredictionScreen()),
    );
  }

  void _navigateToAnalysis(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Navigation vers analyse')));
  }

  void _navigateToAI(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Navigation vers IA')));
  }

  void _navigateToReports(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Navigation vers rapports')));
  }

  void _navigateToOptimization(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Navigation vers optimisation')),
    );
  }

  void _importData(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Import de données')));
  }

  void _navigateToHistory(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Navigation vers historique')));
  }
}
