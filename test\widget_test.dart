// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:predict_av/main.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const PredictAVApp());

    // Verify that the app title is displayed.
    expect(find.text('Predict AV'), findsOneWidget);

    // Verify that welcome section is displayed.
    expect(find.text('Bienvenue dans Predict AV'), findsOneWidget);

    // Verify that statistics section is displayed.
    expect(find.text('Statistiques'), findsOneWidget);
  });

  testWidgets('Navigation works correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const PredictAVApp());

    // Find and tap the floating action button.
    await tester.tap(find.byType(FloatingActionButton));
    await tester.pump();

    // Verify that dialog appears.
    expect(find.text('Nouvelle Prédiction'), findsOneWidget);
  });
}
