import 'package:flutter/material.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart';
import '../widgets/progress_indicator.dart';
import '../theme/app_theme.dart';

class PredictionScreen extends StatefulWidget {
  const PredictionScreen({super.key});

  @override
  State<PredictionScreen> createState() => _PredictionScreenState();
}

class _PredictionScreenState extends State<PredictionScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _dataController = TextEditingController();
  final _parametersController = TextEditingController();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _currentStep = 0;
  bool _isLoading = false;
  bool _showResults = false;
  
  final List<String> _stepLabels = [
    'Configuration',
    'Données',
    'Analyse',
    'Résultats'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _dataController.dispose();
    _parametersController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouvelle Prédiction'),
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProgressIndicator(),
              const SizedBox(height: 24),
              if (!_showResults) _buildForm() else _buildResults(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return ModernCard(
      child: StepProgressIndicator(
        currentStep: _currentStep,
        totalSteps: _stepLabels.length,
        stepLabels: _stepLabels,
        activeColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConfigurationSection(),
          const SizedBox(height: 24),
          _buildDataSection(),
          const SizedBox(height: 24),
          _buildParametersSection(),
          const SizedBox(height: 32),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildConfigurationSection() {
    final theme = Theme.of(context);
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Configuration',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Type de modèle',
              hintText: 'Sélectionnez un modèle',
            ),
            items: const [
              DropdownMenuItem(value: 'linear', child: Text('Régression Linéaire')),
              DropdownMenuItem(value: 'neural', child: Text('Réseau de Neurones')),
              DropdownMenuItem(value: 'random_forest', child: Text('Forêt Aléatoire')),
              DropdownMenuItem(value: 'svm', child: Text('Machine à Vecteurs de Support')),
            ],
            onChanged: (value) {},
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Veuillez sélectionner un modèle';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Objectif',
              hintText: 'Sélectionnez l\'objectif',
            ),
            items: const [
              DropdownMenuItem(value: 'classification', child: Text('Classification')),
              DropdownMenuItem(value: 'regression', child: Text('Régression')),
              DropdownMenuItem(value: 'clustering', child: Text('Clustering')),
              DropdownMenuItem(value: 'forecasting', child: Text('Prévision')),
            ],
            onChanged: (value) {},
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Veuillez sélectionner un objectif';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDataSection() {
    final theme = Theme.of(context);
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.data_usage,
                color: AppTheme.accentColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Données d\'entrée',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _dataController,
            decoration: const InputDecoration(
              labelText: 'Données',
              hintText: 'Entrez vos données (CSV, JSON, etc.)',
            ),
            maxLines: 4,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Veuillez entrer des données';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ModernButton(
                  text: 'Importer Fichier',
                  onPressed: _importFile,
                  icon: Icons.upload_file,
                  isOutlined: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ModernButton(
                  text: 'Exemple',
                  onPressed: _loadExample,
                  icon: Icons.lightbulb_outline,
                  isOutlined: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildParametersSection() {
    final theme = Theme.of(context);
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tune,
                color: AppTheme.secondaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Paramètres avancés',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _parametersController,
            decoration: const InputDecoration(
              labelText: 'Paramètres personnalisés',
              hintText: 'JSON des paramètres (optionnel)',
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Précision vs Vitesse',
                      style: theme.textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    ModernProgressIndicator(
                      value: 0.7,
                      height: 6,
                      valueColor: AppTheme.primaryColor,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        ModernButton(
          text: _isLoading ? 'Analyse en cours...' : 'Lancer l\'analyse',
          onPressed: _isLoading ? null : _runPrediction,
          width: double.infinity,
          isLoading: _isLoading,
          icon: Icons.play_arrow,
          gradient: LinearGradient(
            colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
          ),
        ),
        const SizedBox(height: 12),
        ModernButton(
          text: 'Réinitialiser',
          onPressed: _resetForm,
          width: double.infinity,
          isOutlined: true,
          icon: Icons.refresh,
        ),
      ],
    );
  }

  Widget _buildResults() {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ModernCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: AppTheme.successColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Résultats de l\'analyse',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildResultCard('Précision', '94.2%', Icons.target, AppTheme.successColor),
              const SizedBox(height: 12),
              _buildResultCard('Temps d\'exécution', '2.3s', Icons.speed, AppTheme.accentColor),
              const SizedBox(height: 12),
              _buildResultCard('Confiance', '87%', Icons.verified, AppTheme.primaryColor),
            ],
          ),
        ),
        const SizedBox(height: 24),
        ModernCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Prédictions détaillées',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Résultat: Tendance positive détectée avec une probabilité de 94.2%\n\n'
                  'Facteurs clés:\n'
                  '• Variable A: Impact élevé (+0.85)\n'
                  '• Variable B: Impact modéré (+0.42)\n'
                  '• Variable C: Impact faible (+0.12)',
                  style: theme.textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: ModernButton(
                text: 'Nouvelle Analyse',
                onPressed: _newAnalysis,
                icon: Icons.add,
                isOutlined: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ModernButton(
                text: 'Exporter',
                onPressed: _exportResults,
                icon: Icons.download,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildResultCard(String title, String value, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium,
                ),
                Text(
                  value,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _importFile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fonctionnalité d\'import en développement')),
    );
  }

  void _loadExample() {
    _dataController.text = '''
{
  "data": [
    {"feature1": 1.2, "feature2": 3.4, "target": 0},
    {"feature1": 2.1, "feature2": 4.5, "target": 1},
    {"feature1": 1.8, "feature2": 2.9, "target": 0}
  ]
}''';
  }

  void _runPrediction() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _currentStep = 1;
      });

      // Simulation de l'analyse
      await Future.delayed(const Duration(seconds: 1));
      setState(() => _currentStep = 2);
      
      await Future.delayed(const Duration(seconds: 2));
      setState(() => _currentStep = 3);
      
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _isLoading = false;
        _showResults = true;
      });
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _dataController.clear();
    _parametersController.clear();
    setState(() {
      _currentStep = 0;
      _showResults = false;
    });
  }

  void _newAnalysis() {
    setState(() {
      _showResults = false;
      _currentStep = 0;
    });
    _resetForm();
  }

  void _exportResults() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export des résultats en cours...')),
    );
  }
}
